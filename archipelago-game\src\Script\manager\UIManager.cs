/*
 * UI管理器 - UI系统的核心控制器
 * 作用：统一管理所有UI面板的显示、隐藏和场景切换
 * 设计模式：单例模式，全局唯一实例
 * 使用方式：UIManager.Instance.ShowPanel("PanelName")
 */

using Godot;
using System.Collections.Generic;
using ArchipelagoGame.Interfaces;

namespace ArchipelagoGame.UI
{
	/// <summary>
	/// UI管理器 - 负责管理所有UI面板的生命周期
	/// 单例模式，通过Instance属性访问
	/// </summary>
	public partial class UIManager : Node
	{
		// 单例实例
		private static UIManager _instance;
		/// <summary>单例访问属性</summary>
		public static UIManager Instance => _instance;

		// 面板注册字典：面板名称 -> 面板实例
		private Dictionary<string, IUIPanel> _panels = new();

		// 场景历史记录：用于智能返回功能
		private string _previousScene = "";

		/// <summary>
		/// Godot生命周期 - 初始化单例实例
		/// </summary>
		public override void _Ready()
		{
			_instance = this;
		}

		/// <summary>
		/// 注册UI面板到管理器
		/// 由BaseUIPanel在_Ready时自动调用
		/// </summary>
		public void RegisterPanel(IUIPanel panel)
		{
			if (!_panels.ContainsKey(panel.PanelName))
			{
				_panels[panel.PanelName] = panel;
			}
		}

		/// <summary>
		/// 显示指定名称的UI面板
		/// 示例：ShowPanel("TitleScreen")
		/// </summary>
		public void ShowPanel(string panelName)
		{
			if (_panels.TryGetValue(panelName, out IUIPanel panel))
			{
				panel.Show();
			}
		}

		/// <summary>
		/// 隐藏指定名称的UI面板
		/// 示例：HidePanel("PauseMenu")
		/// </summary>
		public void HidePanel(string panelName)
		{
			if (_panels.TryGetValue(panelName, out IUIPanel panel))
			{
				panel.Hide();
			}
		}

		/// <summary>
		/// 隐藏所有已注册的UI面板
		/// 常用于场景切换前的清理
		/// </summary>
		public void HideAllPanels()
		{
			foreach (var panel in _panels.Values)
			{
				panel.Hide();
			}
		}

		/// <summary>
		/// 切换到指定场景
		/// 参数：scenePath - 场景文件路径（res://开头）
		/// </summary>
		public void SwitchToScene(string scenePath)
		{
			// 记录当前场景作为前一个场景
			_previousScene = GetTree().CurrentScene?.SceneFilePath ?? "";
			GetTree().ChangeSceneToFile(scenePath);
		}

		/// <summary>
		/// 获取前一个场景路径
		/// 用于智能返回功能
		/// </summary>
		public string GetPreviousScene()
		{
			return _previousScene;
		}
	}
}
