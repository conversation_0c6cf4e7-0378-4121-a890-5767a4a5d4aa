using Godot;

namespace ArchipelagoGame.Themes
{
    /// <summary>
    /// 主题类型枚举 - 定义支持的主题类型
    /// </summary>
    public enum ThemeType
    {
        Base,
        Font,
        Color,
        Image,
        Container
    }

    /// <summary>
    /// 主题接口 - 所有主题类型的基础接口
    /// </summary>
    public interface ITheme
    {
        /// <summary>
        /// 应用主题到指定控件
        /// </summary>
        /// <param name="control">目标控件</param>
        void ApplyTo(Control control);
        
        /// <summary>
        /// 主题名称
        /// </summary>
        string ThemeName { get; }
        
        /// <summary>
        /// 主题类型
        /// </summary>
        ThemeType Type { get; }
    }

    /// <summary>
    /// 容器主题接口 - 可以包含多个子主题
    /// </summary>
    public interface IContainerTheme : ITheme
    {
        /// <summary>
        /// 添加子主题
        /// </summary>
        /// <param name="theme">要添加的主题</param>
        void AddTheme(ITheme theme);
    }

    /// <summary>
    /// 基础主题 - 提供基本的主题功能
    /// </summary>
    public abstract class BaseTheme : ITheme
    {
        /// <summary>
        /// 主题名称
        /// </summary>
        public string ThemeName { get; protected set; }
        
        /// <summary>
        /// 主题类型
        /// </summary>
        public ThemeType Type { get; protected set; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <param name="type">主题类型</param>
        protected BaseTheme(string themeName, ThemeType type)
        {
            ThemeName = themeName;
            Type = type;
        }
        
        /// <summary>
        /// 应用主题到指定控件
        /// </summary>
        /// <param name="control">目标控件</param>
        public virtual void ApplyTo(Control control)
        {
            // 基础主题实现 - 子类重写此方法来实现具体的主题应用逻辑
        }
    }
}