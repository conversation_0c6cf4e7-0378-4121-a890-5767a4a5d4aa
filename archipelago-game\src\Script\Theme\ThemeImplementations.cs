using Godot;
using System.Collections.Generic;

namespace ArchipelagoGame.Themes
{
    /// <summary>
    /// 字体主题 - 专门处理字体相关的主题设置
    /// </summary>
    public class FontTheme(FontFile font, int fontSize = 16, string themeName = "FontTheme")
        : BaseTheme(themeName, ThemeType.Font)
    {
        private readonly FontFile _font = font;
        private readonly int _fontSize = fontSize;

        /// <summary>
        /// 应用字体主题到控件
        /// </summary>
        /// <param name="control">目标控件</param>
        public override void ApplyTo(Control control)
        {
            base.ApplyTo(control);

            if (_font == null) return;

            var theme = control.Theme ??= new Theme();
            var controlClass = control.GetClass();

            // 设置通用字体属性
            theme.SetFont("font", controlClass, _font);
            theme.SetFontSize("font_size", controlClass, _fontSize);

            // 为特定控件类型设置字体属性
            switch (control)
            {
                case Label:
                    SetFontProperties(theme, "Label");
                    break;
                case Button:
                    SetFontProperties(theme, "Button");
                    break;
            }
        }

        /// <summary>
        /// 设置指定控件类型的字体属性
        /// </summary>
        private void SetFontProperties(Theme theme, string controlType)
        {
            theme.SetFont("font", controlType, _font);
            theme.SetFontSize("font_size", controlType, _fontSize);
        }
    }

    /// <summary>
    /// 颜色主题 - 专门处理颜色相关的主题设置
    /// </summary>
    public class ColorTheme(Color textColor, Color backgroundColor, string themeName = "ColorTheme", Color? accentColor = null)
        : BaseTheme(themeName, ThemeType.Color)
    {
        private readonly Color _textColor = textColor;
        private readonly Color _backgroundColor = backgroundColor;
        private readonly Color _accentColor = accentColor ?? textColor.Lightened(0.3f);

        /// <summary>
        /// 应用颜色主题到控件
        /// </summary>
        /// <param name="control">目标控件</param>
        public override void ApplyTo(Control control)
        {
            base.ApplyTo(control);

            var theme = control.Theme ??= new Theme();

            // 根据控件类型应用不同的颜色设置
            switch (control)
            {
                case Label:
                    ApplyLabelColors(theme);
                    break;
                case Button:
                    ApplyButtonColors(theme);
                    break;
                case HSlider:
                    ApplySliderColors(theme);
                    break;
                default:
                    theme.SetColor("font_color", control.GetClass(), _textColor);
                    break;
            }
        }

        /// <summary>
        /// 应用标签颜色
        /// </summary>
        private void ApplyLabelColors(Theme theme)
        {
            theme.SetColor("font_color", "Label", _textColor);
            theme.SetColor("font_shadow_color", "Label", _textColor.Darkened(0.5f));
        }

        /// <summary>
        /// 应用按钮颜色
        /// </summary>
        private void ApplyButtonColors(Theme theme)
        {
            var colors = new (string property, Color color)[]
            {
                ("font_color", _textColor),
                ("font_hover_color", _accentColor),
                ("font_pressed_color", _textColor.Darkened(0.2f)),
                ("font_disabled_color", _textColor.Darkened(0.6f))
            };

            foreach (var (property, color) in colors)
            {
                theme.SetColor(property, "Button", color);
            }
        }

        /// <summary>
        /// 应用滑块颜色
        /// </summary>
        private void ApplySliderColors(Theme theme)
        {
            theme.SetColor("grabber_highlight", "HSlider", _accentColor);
            theme.SetColor("grabber_pressed", "HSlider", _accentColor.Darkened(0.2f));
        }
    }

    /// <summary>
    /// 图像主题 - 专门处理图像相关的主题设置
    /// </summary>
    public class ImageTheme(Texture2D backgroundTexture, string themeName = "ImageTheme")
        : BaseTheme(themeName, ThemeType.Image)
    {
        private readonly Texture2D _backgroundTexture = backgroundTexture;

        /// <summary>
        /// 应用图像主题到控件
        /// </summary>
        /// <param name="control">目标控件</param>
        public override void ApplyTo(Control control)
        {
            base.ApplyTo(control);

            // 设置背景图像
            if (_backgroundTexture != null && control is TextureRect textureRect)
            {
                textureRect.Texture = _backgroundTexture;
            }
        }
    }

    /// <summary>
    /// 样式主题 - 处理控件的尺寸、间距、背景等样式属性
    /// </summary>
    public class StyleTheme(
        string themeName = "StyleTheme",
        int buttonMinHeight = 40,
        int buttonMinWidth = 120,
        int containerSpacing = 25, 
        Color? buttonBackgroundColor = null,
        Color? buttonHoverColor = null,
        Color? buttonPressedColor = null) : BaseTheme(themeName, ThemeType.Base)
    {
        private readonly int _buttonMinHeight = buttonMinHeight;
        private readonly int _buttonMinWidth = buttonMinWidth;
        private readonly int _containerSpacing = containerSpacing;
        private readonly Color _buttonBackgroundColor = buttonBackgroundColor ?? new Color(0.0f, 0.0f, 0.0f, 0.0f); // 完全透明背景
        private readonly Color _buttonHoverColor = buttonHoverColor ?? new Color(0.3f, 0.3f, 0.4f, 0.3f); // 半透明悬停效果
        private readonly Color _buttonPressedColor = buttonPressedColor ?? new Color(0.1f, 0.1f, 0.2f, 0.5f); // 半透明按下效果

        /// <summary>
        /// 应用样式主题到控件
        /// </summary>
        /// <param name="control">目标控件</param>
        public override void ApplyTo(Control control)
        {
            base.ApplyTo(control);

            var theme = control.Theme ??= new Theme();

            switch (control)
            {
                case Button:
                    ApplyButtonStyle(theme);
                    break;
                case VBoxContainer vbox:
                    ApplyContainerSpacing(vbox, theme, "separation", "VBoxContainer");
                    break;
                case HBoxContainer hbox:
                    ApplyContainerSpacing(hbox, theme, "separation", "HBoxContainer");
                    break;
                case GridContainer grid:
                    ApplyGridContainerSpacing(grid, theme);
                    break;
                case HSlider:
                    ApplySliderStyle(theme);
                    break;
            }
        }

        /// <summary>
        /// 应用容器间距设置
        /// </summary>
        private void ApplyContainerSpacing(Container container, Theme theme, string property, string controlType)
        {
            container.AddThemeConstantOverride(property, _containerSpacing);
            theme.SetConstant(property, controlType, _containerSpacing);
        }

        /// <summary>
        /// 应用网格容器间距设置
        /// </summary>
        private void ApplyGridContainerSpacing(GridContainer grid, Theme theme)
        {
            var properties = new[] { "h_separation", "v_separation" };
            foreach (var property in properties)
            {
                grid.AddThemeConstantOverride(property, _containerSpacing);
                theme.SetConstant(property, "GridContainer", _containerSpacing);
            }
        }

        /// <summary>
        /// 应用按钮样式
        /// </summary>
        private void ApplyButtonStyle(Theme theme)
        {
            // 创建按钮样式 - 正常状态为完全透明背景
            var normalStyle = CreateButtonStyleBox(_buttonBackgroundColor);
            var hoverStyle = CreateButtonStyleBox(_buttonHoverColor);
            var pressedStyle = CreateButtonStyleBox(_buttonPressedColor);
            var disabledStyle = CreateButtonStyleBox(_buttonBackgroundColor.Darkened(0.3f));
            var focusStyle = new StyleBoxEmpty(); // 完全透明的焦点样式

            // 应用样式到主题
            var buttonStyles = new (string state, StyleBox style)[]
            {
                ("normal", normalStyle),
                ("hover", hoverStyle),
                ("pressed", pressedStyle),
                ("focus", focusStyle),
                ("disabled", disabledStyle)
            };

            foreach (var (state, style) in buttonStyles)
            {
                theme.SetStylebox(state, "Button", style);
            }
        }

        /// <summary>
        /// 创建按钮样式盒
        /// </summary>
        private static StyleBoxFlat CreateButtonStyleBox(Color backgroundColor)
        {
            return new StyleBoxFlat
            {
                BgColor = backgroundColor,
                CornerRadiusTopLeft = 5,
                CornerRadiusTopRight = 5,
                CornerRadiusBottomLeft = 5,
                CornerRadiusBottomRight = 5,
                ContentMarginTop = 8,
                ContentMarginBottom = 8,
                ContentMarginLeft = 16,
                ContentMarginRight = 16,
                BorderWidthTop = 0,
                BorderWidthBottom = 0,
                BorderWidthLeft = 0,
                BorderWidthRight = 0
            };
        }



        /// <summary>
        /// 应用滑块样式
        /// </summary>
        private static void ApplySliderStyle(Theme theme)
        {
            // 创建滑块轨道样式
            var sliderStyle = CreateSliderTrackStyle();

            // 创建抓手样式
            var grabberStyles = new (string state, Color color)[]
            {
                ("grabber_area", new Color(1.0f, 1.0f, 1.0f, 1.0f)), // 纯白色
                ("grabber_area_highlight", new Color(0.7f, 0.8f, 1.0f, 1.0f)), // 淡蓝色
                ("grabber_area_pressed", new Color(0.4f, 0.6f, 1.0f, 1.0f)) // 明显蓝色
            };

            // 应用轨道样式
            theme.SetStylebox("slider", "HSlider", sliderStyle);

            // 应用抓手样式
            foreach (var (state, color) in grabberStyles)
            {
                theme.SetStylebox(state, "HSlider", CreateSliderGrabberStyle(color));
            }

            // 设置滑块行为
            theme.SetConstant("grabber_offset", "HSlider", 0);
            theme.SetConstant("center_grabber", "HSlider", 1);
        }

        /// <summary>
        /// 创建滑块轨道样式
        /// </summary>
        private static StyleBoxFlat CreateSliderTrackStyle()
        {
            return new StyleBoxFlat
            {
                BgColor = new Color(0.8f, 0.8f, 0.8f, 1.0f),
                CornerRadiusTopLeft = 4,
                CornerRadiusTopRight = 4,
                CornerRadiusBottomLeft = 4,
                CornerRadiusBottomRight = 4,
                ContentMarginTop = 2,
                ContentMarginBottom = 2
            };
        }

        /// <summary>
        /// 创建滑块抓手样式
        /// </summary>
        private static StyleBoxFlat CreateSliderGrabberStyle(Color color)
        {
            return new StyleBoxFlat
            {
                BgColor = color,
                CornerRadiusTopLeft = 8,
                CornerRadiusTopRight = 8,
                CornerRadiusBottomLeft = 8,
                CornerRadiusBottomRight = 8,
                ContentMarginTop = 4,
                ContentMarginBottom = 4,
                ContentMarginLeft = 4,
                ContentMarginRight = 4
            };
        }
    }

    /// <summary>
    /// 容器主题 - 可以包含多个子主题的复合主题
    /// </summary>
    public class ContainerTheme(string themeName = "ContainerTheme") : BaseTheme(themeName, ThemeType.Container), IContainerTheme
    {
        private readonly List<ITheme> _childThemes = [];

        /// <summary>
        /// 添加子主题
        /// </summary>
        /// <param name="theme">要添加的主题</param>
        public void AddTheme(ITheme theme)
        {
            if (theme != null && !_childThemes.Contains(theme))
            {
                _childThemes.Add(theme);
            }
        }

        /// <summary>
        /// 应用容器主题到控件 - 依次应用所有子主题
        /// </summary>
        /// <param name="control">目标控件</param>
        public override void ApplyTo(Control control)
        {
            base.ApplyTo(control);

            // 依次应用所有子主题
            foreach (var theme in _childThemes)
            {
                theme.ApplyTo(control);
            }
        }


        public interface ICenter  // 遵循C#命名规范，接口名以I开头
        {
            float X { get; set; }  // 遵循C#命名规范，属性名大写
            float Y { get; set; }
        }

        public class Center : ICenter  // 实现接口的具体类
        {
            public float X { get; set; }
            public float Y { get; set; }
        }

        public class CenterFactory
        {
            public ICenter RetCenter()  // 不需要泛型，直接返回接口类型
            {
                ICenter center = new Center  // 创建具体实现类的实例
                {
                    X = 1f,
                    Y = 0f
                };
                return center;
            }
        }



    }
}