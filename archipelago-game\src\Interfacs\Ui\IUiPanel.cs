/*
 * UI面板接口 - 定义所有UI面板的标准行为
 * 作用：为UI系统提供统一的面板操作规范
 * 关联：与UIManager配合使用，实现统一的面板管理
 */

using Godot;
// 玩家接口
namespace ArchipelagoGame.Interfaces
{
    /// <summary>
    /// UI面板接口 - 所有UI面板必须实现的基本功能
    /// </summary>
    public interface IUIPanel
    {
        /// <summary>显示面板</summary>
        void Show();

        /// <summary>隐藏面板</summary>
        void Hide();

        /// <summary>初始化面板 - 设置UI元素和事件绑定</summary>
        void Initialize();

        /// <summary>面板可见性状态</summary>
        bool IsVisible { get; }

        /// <summary>面板唯一标识名称</summary>
        string PanelName { get; }
    }
}

