/*
 * 洞穴入口 - 可交互的洞穴入口对象
 */

using Godot;
using ArchipelagoGame.Interaction;

namespace ArchipelagoGame.Interaction
{
    /// <summary>
    /// 洞穴入口 - 处理洞穴入口的交互逻辑
    /// 继承自Area2D以获得区域检测功能
    ///
    /// 节点结构要求：
    /// CaveEntrance (Area2D) - 此脚本
    /// ├── CollisionShape2D - 碰撞检测区域
    /// ├── Sprite2D - 洞穴入口图标
    /// └── InteractionPrompt (Label) - 交互提示文本
    /// </summary>
    public partial class CaveEntrance : Area2D
    {
        #region 导出属性

        /// <summary>洞穴场景路径</summary>
        [Export]
        public string CaveScenePath { get; set; } = "res://src/Scenes/GameViwes/CaveScene.tscn";

        /// <summary>洞穴内玩家生成位置</summary>
        [Export]
        public Vector2 CaveSpawnPosition { get; set; } = new Vector2(400, 300);

        /// <summary>洞穴名称</summary>
        [Export]
        public string CaveName { get; set; } = "神秘洞穴";

        /// <summary>是否显示交互提示</summary>
        [Export]
        public bool ShowInteractionPrompt { get; set; } = true;

        #endregion

        #region 私有字段

        /// <summary>洞穴入口精灵</summary>
        private Sprite2D _entranceSprite;

        /// <summary>交互提示标签</summary>
        private Label _promptLabel;

        /// <summary>碰撞形状</summary>
        private CollisionShape2D _collisionShape;

        /// <summary>是否鼠标悬停</summary>
        private bool _isMouseOver = false;

        /// <summary>原始颜色</summary>
        private Color _originalColor = Colors.White;

        #endregion

        #region Godot生命周期

        public override void _Ready()
        {
            // 设置为可交互组
            AddToGroup("interactable");

            // 获取手动创建的子节点引用
            InitializeChildNodes();

            // 连接信号
            InputEvent += OnInputEvent;
            MouseEntered += OnMouseEntered;
            MouseExited += OnMouseExited;

            // 初始化交互提示
            if (_promptLabel != null)
            {
                _promptLabel.Visible = false;
                _promptLabel.Text = $"进入 {CaveName}";
            }

            GD.Print($"洞穴入口已初始化: {CaveName}");
        }
        
        #endregion
        
        #region 信号处理
        
        /// <summary>
        /// 处理输入事件（鼠标点击）
        /// </summary>
        private void OnInputEvent(Node viewport, InputEvent @event, long shapeIdx)
        {
            if (@event is InputEventMouseButton mouseEvent)
            {
                if (mouseEvent.Pressed && mouseEvent.ButtonIndex == MouseButton.Left)
                {
                    EnterCave();
                }
            }
        }
        
        /// <summary>
        /// 鼠标进入区域
        /// </summary>
        private void OnMouseEntered()
        {
            _isMouseOver = true;
            
            // 高亮显示入口
            if (_entranceSprite != null)
            {
                _entranceSprite.Modulate = new Color(1.2f, 1.2f, 1.2f);
            }
            
            // 显示交互提示
            if (_promptLabel != null && ShowInteractionPrompt)
            {
                _promptLabel.Visible = true;
            }
            
            GD.Print($"鼠标进入洞穴入口: {CaveName}");
        }
        
        /// <summary>
        /// 鼠标离开区域
        /// </summary>
        private void OnMouseExited()
        {
            _isMouseOver = false;
            
            // 恢复正常显示
            if (_entranceSprite != null)
            {
                _entranceSprite.Modulate = _originalColor;
            }
            
            // 隐藏交互提示
            if (_promptLabel != null)
            {
                _promptLabel.Visible = false;
            }
        }
        
        #endregion
        
        #region 私有方法

        /// <summary>
        /// 初始化子节点引用
        /// </summary>
        private void InitializeChildNodes()
        {
            // 获取手动创建的子节点引用
            _collisionShape = GetNodeOrNull<CollisionShape2D>("CollisionShape2D");
            _entranceSprite = GetNodeOrNull<Sprite2D>("Sprite2D");
            _promptLabel = GetNodeOrNull<Label>("InteractionPrompt");

            // 验证必要节点是否存在并给出友好提示
            if (_collisionShape == null)
            {
                GD.PrintErr($"洞穴入口 {Name}: 未找到 CollisionShape2D 子节点，请在编辑器中手动添加");
            }

            if (_entranceSprite == null)
            {
                GD.PrintErr($"洞穴入口 {Name}: 未找到 Sprite2D 子节点，请在编辑器中手动添加");
            }
            else
            {
                // 记录原始颜色
                _originalColor = _entranceSprite.Modulate;
            }

            if (_promptLabel == null && ShowInteractionPrompt)
            {
                GD.PrintErr($"洞穴入口 {Name}: 未找到 InteractionPrompt 子节点，交互提示将不可用");
            }
        }
        

        
        /// <summary>
        /// 进入洞穴
        /// </summary>
        private void EnterCave()
        {
            GD.Print($"玩家进入洞穴: {CaveName} -> {CaveScenePath}");

            // 验证场景路径是否有效
            if (string.IsNullOrEmpty(CaveScenePath))
            {
                GD.PrintErr($"洞穴入口 {Name}: 场景路径为空，请在检查器中设置 CaveScenePath");
                return;
            }

            // 检查场景文件是否存在
            if (!ResourceLoader.Exists(CaveScenePath))
            {
                GD.PrintErr($"洞穴入口 {Name}: 场景文件不存在: {CaveScenePath}");
                return;
            }

            // 通过交互管理器切换场景
            if (InteractionManager.Instance != null)
            {
                InteractionManager.Instance.TransitionToCave(CaveScenePath, CaveSpawnPosition);
            }
            else
            {
                GD.PrintErr("InteractionManager 实例不存在！请检查 project.godot 中的 autoload 配置");
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置入口纹理（运行时调用）
        /// </summary>
        public void SetEntranceTexture(Texture2D texture)
        {
            if (_entranceSprite != null)
            {
                _entranceSprite.Texture = texture;
            }
        }
        
        /// <summary>
        /// 设置碰撞区域大小（运行时调用）
        /// </summary>
        public void SetCollisionSize(Vector2 size)
        {
            if (_collisionShape?.Shape is RectangleShape2D rectShape)
            {
                rectShape.Size = size;
            }
        }

        /// <summary>
        /// 更新交互提示文本
        /// </summary>
        public void UpdatePromptText()
        {
            if (_promptLabel != null)
            {
                _promptLabel.Text = $"点击进入 {CaveName}";
            }
        }
        
        #endregion
    }
}
