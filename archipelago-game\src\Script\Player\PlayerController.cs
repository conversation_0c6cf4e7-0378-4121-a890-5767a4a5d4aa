/*
 * 玩家控制器 - 等距视角移动系统
 */

using Godot;
using ArchipelagoGame.Interfaces;

namespace ArchipelagoGame.Player
{
    /// <summary>
    /// 玩家控制器 - 实现等距视角的角色移动
    /// 继承自CharacterBody2D以获得物理移动和碰撞检测功能
    /// </summary>
    public partial class PlayerController : CharacterBody2D, IPlayer
    {
        #region 导出属性 - 可在编辑器中调整

        /// <summary>玩家数据资源</summary>
        [Export]
        public PlayerData PlayerData { get; set; }

        #endregion

        #region 私有字段

        /// <summary>目标速度向量 - 用于平滑移动</summary>
        private Vector2 _targetVelocity = Vector2.Zero;

        /// <summary>状态机组件</summary>
        private PlayerStateMachine _stateMachine;

        /// <summary>交互组件</summary>
        private PlayerInteraction _interaction;

        #endregion

        #region IPlayer接口实现

        /// <summary>移动速度（像素/秒）</summary>
        public float Speed 
        { 
            get => PlayerData?.Speed ?? 600.0f; 
            set 
            { 
                if (PlayerData != null) 
                    PlayerData.Speed = value; 
            } 
        }

        #endregion

        #region Godot生命周期

        /// <summary>
        /// 节点准备就绪时初始化
        /// </summary>
        public override void _Ready()
        {
            // 添加到玩家组，用于场景切换时的位置管理
            AddToGroup("player");

            // 如果没有设置PlayerData，创建默认的
            if (PlayerData == null)
            {
                PlayerData = new PlayerData();
            }

            // 添加状态机组件
            _stateMachine = new PlayerStateMachine();
            _stateMachine.Name = "PlayerStateMachine";
            AddChild(_stateMachine);

            // 添加交互组件
            _interaction = new PlayerInteraction();
            _interaction.Name = "PlayerInteraction";
            AddChild(_interaction);
        }

        /// <summary>
        /// 物理帧更新 - 处理移动逻辑
        /// </summary>
        /// <param name="delta">帧时间间隔</param>
        public override void _PhysicsProcess(double delta)
        {
            HandleMovement(delta);
            
            // 通知状态机更新动画
            Vector2 inputDirection = GetInputDirection();
            _stateMachine?.UpdateAnimation(inputDirection);
        }

        #endregion

        #region IPlayer接口实现

        /// <summary>
        /// 处理玩家移动逻辑
        /// </summary>
        /// <param name="delta">帧时间间隔</param>
        public void HandleMovement(double delta)
        {
            // 获取输入方向
            Vector2 inputDirection = GetInputDirection();

            if (inputDirection != Vector2.Zero)
            {
                // 转换为等距坐标系的移动向量
                Vector2 isoMovement = ConvertToIsometric60Degree(inputDirection);

                // 设置目标速度
                _targetVelocity = isoMovement * Speed;
            }
            else
            {
                // 没有输入时逐渐停止
                _targetVelocity = Vector2.Zero;
            }

            // 平滑插值到目标速度
            float smoothing = PlayerData?.MovementSmoothing ?? 10.0f;
            Velocity = Velocity.Lerp(_targetVelocity, smoothing * (float)delta);

            // 使用Godot的内置移动方法进行移动和碰撞检测
            MoveAndSlide();
        }

        /// <summary>
        /// 获取输入方向，支持WASD和方向键
        /// </summary>
        /// <returns>标准化的输入方向向量</returns>
        public Vector2 GetInputDirection()
        {
            Vector2 direction = Vector2.Zero;

            // 检测WASD键和方向键
            if (Input.IsActionPressed("ui_left") || Input.IsKeyPressed(Key.A))
                direction.X -= 1;
            if (Input.IsActionPressed("ui_right") || Input.IsKeyPressed(Key.D))
                direction.X += 1;
            if (Input.IsActionPressed("ui_up") || Input.IsKeyPressed(Key.W))
                direction.Y -= 1;
            if (Input.IsActionPressed("ui_down") || Input.IsKeyPressed(Key.S))
                direction.Y += 1;

            // 返回标准化的方向向量，确保对角线移动速度一致
            return direction.Normalized();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 将2D输入转换为等距视角的移动方向
        /// </summary>
        /// <param name="inputDir">输入方向向量</param>
        /// <returns>转换后的等距移动向量</returns>
        private static Vector2 ConvertToIsometric60Degree(Vector2 inputDir)
        {
            // 三角函数值
            float cos60 = 0.5f;        // cos(60°)
            float sin60 = 0.866025f;   // sin(60°)
            // 等距地图方向映射
            // 计算等距坐标向量
            // 使用旋转矩阵将标准方向映射到等距方向
            // X轴（A/D键）：A键为西南方向 (-sin60, -cos60)，D键为东北方向 (sin60, cos60)
            // Y轴（W/S键）：W键为西北方向 (-sin60, cos60)，S键为东南方向 (sin60, -cos60)
            float isoX = inputDir.X * sin60 + inputDir.Y * (-sin60);
            float isoY = inputDir.X * cos60 + inputDir.Y * cos60;

            // 返回归一化向量，确保移动速度一致性
            return new Vector2(isoX, isoY).Normalized();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 播放攻击动画 - 保持向后兼容
        /// </summary>
        public void PlayAttackAnimation()
        {
            _stateMachine?.PlayAttackAnimation();
        }

        /// <summary>
        /// 获取状态机组件
        /// </summary>
        /// <returns>状态机组件</returns>
        public PlayerStateMachine GetStateMachine()
        {
            return _stateMachine;
        }

        /// <summary>
        /// 获取交互组件
        /// </summary>
        /// <returns>交互组件</returns>
        public PlayerInteraction GetInteraction()
        {
            return _interaction;
        }

        #endregion
    }
}
