/*
 * 玩家接口 - 定义玩家角色的基础行为
 * 作用：规范玩家类的实现，确保一致性
 */

using Godot;

namespace ArchipelagoGame.Interfaces
{
    /// <summary>
    /// 玩家接口 - 定义玩家角色的基础功能
    /// </summary>
    public interface IPlayer
    {
        /// <summary>移动速度</summary>
        float Speed { get; set; }

        /// <summary>当前位置</summary>
        Vector2 Position { get; set; }

        /// <summary>当前速度向量</summary>
        Vector2 Velocity { get; set; }

        /// <summary>
        /// 处理玩家移动
        /// </summary>
        /// <param name="delta">帧时间间隔</param>
        void HandleMovement(double delta);

        /// <summary>
        /// 获取输入方向
        /// </summary>
        /// <returns>标准化的输入方向向量</returns>
        Vector2 GetInputDirection();
    }
}