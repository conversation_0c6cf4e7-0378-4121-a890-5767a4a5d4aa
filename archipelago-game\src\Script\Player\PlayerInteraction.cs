/*
 * 玩家交互系统 - 负责交互功能
 */

using Godot;
using ArchipelagoGame.Interaction;

namespace ArchipelagoGame.Player
{
    /// <summary>
    /// 玩家交互系统 - 负责交互系统，预留未来扩展
    /// 继承自Node以作为游戏对象的组件
    /// </summary>
    public partial class PlayerInteraction : Node
    {
        #region 信号定义

        /// <summary>交互开始信号</summary>
        [Signal]
        public delegate void InteractionStartedEventHandler(Node target);

        /// <summary>交互结束信号</summary>
        [Signal]
        public delegate void InteractionEndedEventHandler(Node target);

        #endregion

        #region 私有字段

        /// <summary>当前可交互的目标</summary>
        private Node _currentInteractionTarget;

        #endregion

        #region Godot生命周期

        /// <summary>
        /// 节点准备就绪时初始化
        /// </summary>
        public override void _Ready()
        {
            // 延迟处理场景切换，确保 InteractionManager 已完全初始化
            CallDeferred(MethodName.HandleSceneTransition);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置交互目标
        /// </summary>
        /// <param name="target">交互目标</param>
        public void SetInteractionTarget(Node target)
        {
            if (_currentInteractionTarget != target)
            {
                if (_currentInteractionTarget != null)
                {
                    EmitSignal(SignalName.InteractionEnded, _currentInteractionTarget);
                }

                _currentInteractionTarget = target;

                if (_currentInteractionTarget != null)
                {
                    EmitSignal(SignalName.InteractionStarted, _currentInteractionTarget);
                }
            }
        }

        /// <summary>
        /// 执行交互
        /// </summary>
        public void Interact()
        {
            if (_currentInteractionTarget != null)
            {
                // 预留交互逻辑实现
                GD.Print($"与 {_currentInteractionTarget.Name} 进行交互");
            }
        }

        /// <summary>
        /// 清除交互目标
        /// </summary>
        public void ClearInteractionTarget()
        {
            SetInteractionTarget(null);
        }

        #endregion

        #region 场景切换支持

        /// <summary>
        /// 处理场景切换后的玩家位置设置
        /// </summary>
        public void HandleSceneTransition()
        {
            var interactionManager = InteractionManager.Instance;
            if (interactionManager != null && interactionManager.ShouldSetPlayerPosition())
            {
                Vector2 spawnPosition = interactionManager.GetPlayerSpawnPosition();
                if (spawnPosition != Vector2.Zero)
                {
                    var player = GetParent() as CharacterBody2D;
                    if (player != null)
                    {
                        player.GlobalPosition = spawnPosition;
                        interactionManager.MarkPlayerPositionSet();
                        GD.Print($"玩家位置已设置为: {spawnPosition}");
                    }
                }
            }
        }

        #endregion
    }
}
