/*
 * 交互管理器 - 负责场景切换和交互状态管理
 */

using Godot;
using System.Collections.Generic;
using ArchipelagoGame.UI;

namespace ArchipelagoGame.Interaction
{
    /// <summary>
    /// 交互管理器 - 统一管理场景切换和交互逻辑
    /// 单例模式，全局唯一实例
    /// </summary>
    public partial class InteractionManager : Node
    {
        #region 单例模式
        
        private static InteractionManager _instance;
        public static InteractionManager Instance => _instance;
        
        #endregion
        
        #region 信号定义
        
        /// <summary>场景切换开始信号</summary>
        [Signal]
        public delegate void SceneTransitionStartedEventHandler(string fromScene, string toScene);
        
        /// <summary>场景切换完成信号</summary>
        [Signal]
        public delegate void SceneTransitionCompletedEventHandler(string sceneName);
        
        /// <summary>玩家位置设置信号</summary>
        [Signal]
        public delegate void PlayerPositionSetEventHandler(Vector2 position);
        
        #endregion
        
        #region 私有字段
        
        /// <summary>场景状态数据</summary>
        private Dictionary<string, SceneStateData> _sceneStates = new();
        
        /// <summary>当前场景路径</summary>
        private string _currentScenePath = "";
        
        /// <summary>玩家生成位置</summary>
        private Vector2 _playerSpawnPosition = Vector2.Zero;
        
        /// <summary>是否需要设置玩家位置</summary>
        private bool _shouldSetPlayerPosition = false;
        
        #endregion
        
        #region Godot生命周期
        
        public override void _Ready()
        {
            _instance = this;
            _currentScenePath = GetTree().CurrentScene?.SceneFilePath ?? "";
            
            // 连接场景切换信号
            GetTree().NodeAdded += OnNodeAdded;
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 切换到洞穴场景
        /// </summary>
        /// <param name="caveScenePath">洞穴场景路径</param>
        /// <param name="spawnPosition">玩家生成位置</param>
        public void TransitionToCave(string caveScenePath, Vector2 spawnPosition)
        {
            GD.Print($"切换到洞穴场景: {caveScenePath}, 生成位置: {spawnPosition}");
            
            // 保存当前场景状态
            SaveCurrentSceneState();
            
            // 设置玩家生成位置
            _playerSpawnPosition = spawnPosition;
            _shouldSetPlayerPosition = true;
            
            // 发出场景切换开始信号
            EmitSignal(SignalName.SceneTransitionStarted, _currentScenePath, caveScenePath);
            
            // 使用UIManager进行场景切换
            UIManager.Instance?.SwitchToScene(caveScenePath);
        }
        
        /// <summary>
        /// 返回主世界
        /// </summary>
        /// <param name="returnPosition">返回位置</param>
        public void ReturnToMainWorld(Vector2 returnPosition)
        {
            string mainWorldPath = "res://src/Scenes/GameViwes/MainWorld.tscn";
            GD.Print($"返回主世界: {mainWorldPath}, 返回位置: {returnPosition}");
            
            _playerSpawnPosition = returnPosition;
            _shouldSetPlayerPosition = true;
            
            EmitSignal(SignalName.SceneTransitionStarted, _currentScenePath, mainWorldPath);
            UIManager.Instance?.SwitchToScene(mainWorldPath);
        }
        
        /// <summary>
        /// 获取玩家生成位置
        /// </summary>
        public Vector2 GetPlayerSpawnPosition()
        {
            return _playerSpawnPosition;
        }
        
        /// <summary>
        /// 检查是否需要设置玩家位置
        /// </summary>
        public bool ShouldSetPlayerPosition()
        {
            return _shouldSetPlayerPosition;
        }
        
        /// <summary>
        /// 标记玩家位置已设置
        /// </summary>
        public void MarkPlayerPositionSet()
        {
            _shouldSetPlayerPosition = false;
            EmitSignal(SignalName.PlayerPositionSet, _playerSpawnPosition);
        }
        
        /// <summary>
        /// 获取场景状态数据
        /// </summary>
        public SceneStateData GetSceneState(string scenePath)
        {
            return _sceneStates.TryGetValue(scenePath, out var state) ? state : null;
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 保存当前场景状态
        /// </summary>
        private void SaveCurrentSceneState()
        {
            var player = GetTree().GetFirstNodeInGroup("player") as Node2D;
            if (player != null)
            {
                var stateData = new SceneStateData
                {
                    ScenePath = _currentScenePath,
                    PlayerPosition = player.GlobalPosition,
                    Timestamp = Time.GetUnixTimeFromSystem()
                };
                
                _sceneStates[_currentScenePath] = stateData;
                GD.Print($"保存场景状态: {_currentScenePath}, 玩家位置: {player.GlobalPosition}");
            }
        }
        
        /// <summary>
        /// 当新节点添加到场景树时调用
        /// </summary>
        private void OnNodeAdded(Node node)
        {
            // 检测玩家节点添加，用于设置位置
            if (node.IsInGroup("player") && _shouldSetPlayerPosition)
            {
                CallDeferred(MethodName.SetPlayerPositionDeferred, node);
            }
        }
        
        /// <summary>
        /// 延迟设置玩家位置
        /// </summary>
        private void SetPlayerPositionDeferred(Node playerNode)
        {
            if (playerNode is Node2D player && _shouldSetPlayerPosition)
            {
                player.GlobalPosition = _playerSpawnPosition;
                MarkPlayerPositionSet();
                GD.Print($"设置玩家位置: {_playerSpawnPosition}");
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 场景状态数据类
    /// </summary>
    public class SceneStateData
    {
        public string ScenePath { get; set; } = "";
        public Vector2 PlayerPosition { get; set; } = Vector2.Zero;
        public double Timestamp { get; set; } = 0.0;
    }
}
