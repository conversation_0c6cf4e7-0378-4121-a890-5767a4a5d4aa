[folding]

node_unfolds=[<PERSON><PERSON><PERSON><PERSON>("Player"), PackedStringArray("Floor", "Transform"), NodePath("Player/PlayerSprite"), PackedStringArray("texture", "Animation", "Transform", "Offset", "Region", "Texture"), NodePath("Player/PlayerCollisionShap2D"), PackedStringArray("Transform"), NodePath("Player/AnimationPlayer"), PackedStringArray("Playback Options")]
resource_unfolds=["res://src/Scenes/player.tscn::ShaderMaterial_3f673", PackedStringArray(), "res://src/Scenes/player.tscn::CapsuleShape2D_k2co4", PackedStringArray()]
nodes_folded=[NodePath("Player/PlayerCamera2D"), NodePath("Player/PlayerCamera2D/voidBackground_root"), NodePath("Player/PlayerCamera2D/Control")]
