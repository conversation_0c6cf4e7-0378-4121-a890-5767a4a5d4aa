/*
 * 洞穴出口 - 返回主世界的出口点
 */

using Godot;
using ArchipelagoGame.Interaction;

namespace ArchipelagoGame.Interaction
{
    /// <summary>
    /// 洞穴出口 - 处理从洞穴返回主世界的逻辑
    /// 继承自Area2D以获得区域检测功能
    /// </summary>
    public partial class CaveExit : Area2D
    {
        #region 导出属性
        
        /// <summary>返回主世界时的玩家位置</summary>
        [Export]
        public Vector2 ReturnPosition { get; set; } = new Vector2(400, 400);
        
        /// <summary>出口名称</summary>
        [Export]
        public string ExitName { get; set; } = "洞穴出口";
        
        /// <summary>是否显示交互提示</summary>
        [Export]
        public bool ShowInteractionPrompt { get; set; } = true;
        
        /// <summary>出口图标纹理</summary>
        [Export]
        public Texture2D ExitTexture { get; set; }
        
        #endregion
        
        #region 私有字段
        
        /// <summary>出口精灵</summary>
        private Sprite2D _exitSprite;
        
        /// <summary>交互提示标签</summary>
        private Label _promptLabel;
        
        /// <summary>碰撞形状</summary>
        private CollisionShape2D _collisionShape;
        
        /// <summary>是否鼠标悬停</summary>
        private bool _isMouseOver = false;
        
        /// <summary>原始颜色</summary>
        private Color _originalColor = Colors.White;
        
        #endregion
        
        #region Godot生命周期
        
        public override void _Ready()
        {
            // 设置为可交互组
            AddToGroup("interactable");
            
            // 创建子节点
            CreateChildNodes();
            
            // 连接信号
            InputEvent += OnInputEvent;
            MouseEntered += OnMouseEntered;
            MouseExited += OnMouseExited;
            
            GD.Print($"洞穴出口已初始化: {ExitName}");
        }
        
        #endregion
        
        #region 信号处理
        
        /// <summary>
        /// 处理输入事件（鼠标点击）
        /// </summary>
        private void OnInputEvent(Node viewport, InputEvent @event, long shapeIdx)
        {
            if (@event is InputEventMouseButton mouseEvent)
            {
                if (mouseEvent.Pressed && mouseEvent.ButtonIndex == MouseButton.Left)
                {
                    ExitCave();
                }
            }
        }
        
        /// <summary>
        /// 鼠标进入区域
        /// </summary>
        private void OnMouseEntered()
        {
            _isMouseOver = true;
            
            // 高亮显示出口
            if (_exitSprite != null)
            {
                _exitSprite.Modulate = new Color(1.2f, 1.2f, 1.2f);
            }
            
            // 显示交互提示
            if (_promptLabel != null && ShowInteractionPrompt)
            {
                _promptLabel.Visible = true;
            }
            
            GD.Print($"鼠标进入洞穴出口: {ExitName}");
        }
        
        /// <summary>
        /// 鼠标离开区域
        /// </summary>
        private void OnMouseExited()
        {
            _isMouseOver = false;
            
            // 恢复正常显示
            if (_exitSprite != null)
            {
                _exitSprite.Modulate = _originalColor;
            }
            
            // 隐藏交互提示
            if (_promptLabel != null)
            {
                _promptLabel.Visible = false;
            }
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 创建子节点
        /// </summary>
        private void CreateChildNodes()
        {
            // 创建碰撞形状
            _collisionShape = new CollisionShape2D();
            _collisionShape.Name = "CollisionShape2D";
            var shape = new RectangleShape2D();
            shape.Size = new Vector2(128, 128); // 默认大小
            _collisionShape.Shape = shape;
            AddChild(_collisionShape);
            
            // 创建精灵节点
            _exitSprite = new Sprite2D();
            _exitSprite.Name = "Sprite2D";
            if (ExitTexture != null)
            {
                _exitSprite.Texture = ExitTexture;
            }
            else
            {
                // 如果没有纹理，创建一个简单的占位符
                CreatePlaceholderTexture();
            }
            AddChild(_exitSprite);
            
            // 创建交互提示标签
            _promptLabel = new Label();
            _promptLabel.Name = "InteractionPrompt";
            _promptLabel.Text = $"点击返回主世界";
            _promptLabel.Position = new Vector2(-60, -80); // 在出口上方显示
            _promptLabel.Visible = false;
            _promptLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat());
            AddChild(_promptLabel);
        }
        
        /// <summary>
        /// 创建占位符纹理
        /// </summary>
        private void CreatePlaceholderTexture()
        {
            // 创建一个简单的箭头形状占位符
            var image = Image.Create(64, 64, false, Image.Format.Rgba8);
            image.Fill(new Color(0.1f, 0.5f, 0.1f, 0.8f)); // 绿色半透明
            
            // 绘制一个简单的向上箭头
            for (int x = 20; x < 44; x++)
            {
                for (int y = 10; y < 54; y++)
                {
                    // 箭头主体
                    if (x >= 28 && x <= 36 && y >= 20)
                    {
                        image.SetPixel(x, y, new Color(0.0f, 0.8f, 0.0f, 1.0f));
                    }
                    // 箭头头部
                    else if (y >= 10 && y <= 25)
                    {
                        int centerX = 32;
                        int distance = Mathf.Abs(x - centerX);
                        int maxDistance = (y - 10) / 2 + 4;
                        if (distance <= maxDistance)
                        {
                            image.SetPixel(x, y, new Color(0.0f, 0.8f, 0.0f, 1.0f));
                        }
                    }
                }
            }
            
            var texture = ImageTexture.CreateFromImage(image);
            _exitSprite.Texture = texture;
        }
        
        /// <summary>
        /// 离开洞穴
        /// </summary>
        private void ExitCave()
        {
            GD.Print($"玩家离开洞穴: {ExitName} -> 主世界");
            
            // 通过交互管理器返回主世界
            if (InteractionManager.Instance != null)
            {
                InteractionManager.Instance.ReturnToMainWorld(ReturnPosition);
            }
            else
            {
                GD.PrintErr("InteractionManager 实例不存在！");
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置出口纹理
        /// </summary>
        public void SetExitTexture(Texture2D texture)
        {
            ExitTexture = texture;
            if (_exitSprite != null)
            {
                _exitSprite.Texture = texture;
            }
        }
        
        /// <summary>
        /// 设置碰撞区域大小
        /// </summary>
        public void SetCollisionSize(Vector2 size)
        {
            if (_collisionShape?.Shape is RectangleShape2D rectShape)
            {
                rectShape.Size = size;
            }
        }
        
        #endregion
    }
}
