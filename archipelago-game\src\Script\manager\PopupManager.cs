using Godot;
using ArchipelagoGame.Themes;

namespace ArchipelagoGame.UI
{
    /// <summary>
    /// 弹窗管理器 - 提供弹窗主题应用功能
    /// 作用：桥接GDScript和C#主题系统
    /// </summary>
    public partial class PopupManager : Node
    {
        private static PopupManager _instance;
        public static PopupManager Instance => _instance;

        public override void _Ready()
        {
            _instance = this;
            // 确保主题管理器已初始化
            ThemeManager.Instance?.CreateBaseThemes();
        }

        /// <summary>
        /// 为弹窗容器应用主题
        /// </summary>
        public void ApplyPopupTheme(Control popup)
        {
            if (popup == null) return;
            
            // 应用容器主题
            ThemeManager.Instance?.ApplyTheme(popup, "DefaultContainerTheme");
            
            // 为弹窗设置半透明背景
            if (popup is Panel panel)
            {
                var styleBox = new StyleBoxFlat
                {
                    BgColor = new Color(0.0f, 0.0f, 0.0f, 0.7f),
                    CornerRadiusTopLeft = 10,
                    CornerRadiusTopRight = 10,
                    CornerRadiusBottomLeft = 10,
                    CornerRadiusBottomRight = 10
                };
                panel.AddThemeStyleboxOverride("panel", styleBox);
            }
        }

        /// <summary>
        /// 为按钮应用主题
        /// </summary>
        public void ApplyButtonTheme(Button button)
        {
            if (button == null) return;
            ThemeApplicator.ApplyButtonTheme(button);
        }

        /// <summary>
        /// 为标签应用主题
        /// </summary>
        public void ApplyLabelTheme(Label label)
        {
            if (label == null) return;
            ThemeManager.Instance?.ApplyTheme(label, "DefaultFontTheme");
            ThemeManager.Instance?.ApplyTheme(label, "DefaultColorTheme");
        }

        /// <summary>
        /// 为容器应用主题
        /// </summary>
        public void ApplyContainerTheme(Container container)
        {
            if (container == null) return;
            ThemeManager.Instance?.ApplyTheme(container, "DefaultContainerTheme");
        }
    }
}