# 主场景弹窗控制器
# 作用：管理所有弹窗的显示/隐藏，集中处理弹窗逻辑
# 参考JavaScript设计：单一职责、可复用、减少冗余代码

extends Control
class_name MainScenePopupController

# 弹窗节点引用（根据实际场景结构）
@onready var popup_overlay: Control = $PopupOverlay
@onready var settings_popup: Panel = $"PopupOverlay/​​SettingsPop-up"
@onready var inventory_popup: Panel = $PopupOverlay/Backpack
@onready var dialog_popup: Panel = $PopupOverlay/DialogPopup

# 触发按钮引用
@onready var settings_btn: Button = $TriggerButtons/SettingsButton
@onready var inventory_btn: Button = $TriggerButtons/InventoryButton
@onready var dialog_btn: Button = $TriggerButtons/DialogButton

# 弹窗内按钮引用
@onready var save_game_btn: Button = $"PopupOverlay/​​SettingsPop-up/SetButton/SaveGame"
@onready var game_settings_btn: Button = $"PopupOverlay/​​SettingsPop-up/SetButton/GameSettings"
@onready var exit_game_btn: Button = $"PopupOverlay/​​SettingsPop-up/SetButton/ExitGame"
@onready var return_to_start_btn: Button = $"PopupOverlay/​​SettingsPop-up/SetButton/ReturnToStart​"
@onready var settings_return_btn: Button = $"PopupOverlay/​​SettingsPop-up/SetButton/Return"

@onready var send_btn: Button = $PopupOverlay/DialogPopup/DialogueButton/Send
@onready var dialog_return_btn: Button = $PopupOverlay/DialogPopup/DialogueButton/Return

@onready var inventory_return_btn: Button = $PopupOverlay/Backpack/SettingsButton

# C#弹窗管理器引用
var popup_manager: Node

func _ready():
	# 获取C#弹窗管理器实例
	popup_manager = get_node("/root/PopupManager")

	# 连接触发按钮事件
	connect_trigger_buttons()

	# 连接弹窗内按钮事件
	connect_popup_buttons()

	# 应用主题
	apply_themes()

	# 初始隐藏所有弹窗
	hide_all_popups()

# 连接弹窗内按钮事件
func connect_popup_buttons():
	# 设置弹窗按钮
	save_game_btn.pressed.connect(save_game)
	game_settings_btn.pressed.connect(open_game_settings)
	exit_game_btn.pressed.connect(exit_game)
	return_to_start_btn.pressed.connect(return_to_start)
	settings_return_btn.pressed.connect(hide_all_popups)

	# 对话弹窗按钮
	send_btn.pressed.connect(send_message)
	dialog_return_btn.pressed.connect(hide_all_popups)

	# 背包弹窗按钮
	inventory_return_btn.pressed.connect(hide_all_popups)

# 应用主题到所有UI元素
func apply_themes():
	if popup_manager == null:
		return

	# 应用主题到触发按钮
	apply_trigger_button_themes()

	# 应用主题到弹窗面板
	apply_popup_panel_themes()

	# 应用主题到弹窗内按钮
	apply_popup_button_themes()

# 应用触发按钮主题
func apply_trigger_button_themes():
	var trigger_buttons = [settings_btn, inventory_btn, dialog_btn]
	for button in trigger_buttons:
		popup_manager.call("ApplyButtonTheme", button)

# 应用弹窗面板主题
func apply_popup_panel_themes():
	var popup_panels = [settings_popup, inventory_popup, dialog_popup]
	for panel in popup_panels:
		popup_manager.call("ApplyPopupTheme", panel)

# 应用弹窗内按钮主题
func apply_popup_button_themes():
	var popup_buttons = [
		save_game_btn, game_settings_btn, exit_game_btn,
		return_to_start_btn, settings_return_btn, send_btn,
		dialog_return_btn, inventory_return_btn
	]
	for button in popup_buttons:
		popup_manager.call("ApplyButtonTheme", button)

# 连接触发按钮事件
func connect_trigger_buttons():
	settings_btn.pressed.connect(_on_trigger_button_pressed.bind("settings"))
	inventory_btn.pressed.connect(_on_trigger_button_pressed.bind("inventory"))
	dialog_btn.pressed.connect(_on_trigger_button_pressed.bind("dialog"))

# 触发按钮点击事件
func _on_trigger_button_pressed(popup_type: String):
	toggle_popup(popup_type)

# 切换弹窗显示状态（类似JavaScript的切换逻辑）
func toggle_popup(popup_type: String):
	var target_popup = get_popup_by_type(popup_type)
	
	if target_popup.visible:
		hide_all_popups()
	else:
		hide_all_popups()
		show_popup(target_popup)

# 根据类型获取弹窗
func get_popup_by_type(popup_type: String) -> Panel:
	match popup_type:
		"settings":
			return settings_popup
		"inventory":
			return inventory_popup
		"dialog":
			return dialog_popup
		_:
			return settings_popup

# 显示弹窗
func show_popup(popup: Panel):
	popup_overlay.visible = true
	popup.visible = true

# 隐藏所有弹窗
func hide_all_popups():
	popup_overlay.visible = false
	settings_popup.visible = false
	inventory_popup.visible = false
	dialog_popup.visible = false

# 弹窗内按钮点击事件（备用处理函数）
func _on_popup_button_pressed(button_text: String, _popup_title: String):
	match button_text:
		"返回", "取消":
			hide_all_popups()
		"保存游戏":
			save_game()
		"游戏设置":
			open_game_settings()
		"退出游戏":
			exit_game()
		"发送":
			send_message()

# ==================== 按钮功能实现 ====================

# 保存游戏功能
func save_game():
	# 这里可以添加实际的保存逻辑
	# 例如：保存玩家数据、游戏进度等
	pass

# 打开游戏设置
func open_game_settings():
	# 通过UIManager跳转到设置场景以支持智能返回功能
	var ui_manager = get_node("/root/UiManager")
	if ui_manager:
		ui_manager.call("SwitchToScene", "res://src/Scenes/SettingsViews/GameSettings.tscn")
	else:
		# 备用方案：直接切换场景
		get_tree().change_scene_to_file("res://src/Scenes/SettingsViews/GameSettings.tscn")

# 退出游戏
func exit_game():
	get_tree().quit()

# 返回开始页面
func return_to_start():
	# 通过UIManager直接切换到开始游戏场景
	var ui_manager = get_node("/root/UiManager")
	if ui_manager:
		ui_manager.call("SwitchToScene", "res://src/Scenes/StartGame/StartGame.tscn")
	else:
		# 备用方案：直接切换场景
		get_tree().change_scene_to_file("res://src/Scenes/StartGame/StartGame.tscn")

# 发送消息功能
func send_message():
	# TODO: 实现发送消息功能
	pass
	

# 处理ESC键关闭弹窗
func _input(event):
	if event.is_action_pressed("ui_cancel"):
		hide_all_popups()
