[folding]

node_unfolds=[Node<PERSON><PERSON>("Environment/TileMapLayer"), PackedStringArray("Rendering", "tile_set"), NodePath("CaveExit1/CollisionShape2D"), PackedStringArray("shape", "Transform")]
resource_unfolds=["res://src/Scenes/GameViwes/CaveScene.tscn::TileSet_mkl61", PackedStringArray("terrain_set__array", "terrain_set_0/terrain__array", "physics_layer__array"), "res://src/Scenes/GameViwes/CaveScene.tscn::CircleShape2D_ed0bx", PackedStringArray()]
nodes_folded=[]
