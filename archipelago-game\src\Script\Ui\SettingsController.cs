/*
 * 设置界面控制器 - 游戏设置界面控制器
 * 作用：管理设置界面的UI交互和配置保存
 * 继承：BaseUIPanel -> SettingsController
 * 场景结构：需要包含设置选项和返回按钮
 */

using Godot;
using ArchipelagoGame.Themes;

namespace ArchipelagoGame.UI
{
    /// <summary>
    /// 设置界面控制器 - 管理游戏设置界面
    /// 提供音量、画质、控制等设置选项
    /// </summary>
    public partial class SettingsController : BaseUIPanel
    {
        /// <summary>面板标识名称</summary>
        public override string PanelName => "Settings";

        // UI控件引用
        private VBoxContainer _settingsContainer;
        private Button _backButton;
        private Label _titleLabel;

        // 音量控制组件
        private readonly VolumeControl[] _volumeControls = new VolumeControl[3];

        /// <summary>
        /// 音量控制数据结构
        /// </summary>
        private record VolumeControl(string Name, HSlider Slider, Label Label, double DefaultValue);

        /// <summary>
        /// 初始化面板 - 获取UI元素并绑定事件
        /// </summary>
        public override void Initialize()
        {
            InitializeUIComponents();
            ConnectEvents();
            LoadSettings();
            ApplyThemes();
        }

        /// <summary>
        /// 初始化UI组件
        /// </summary>
        private void InitializeUIComponents()
        {
            _settingsContainer = GetNode<VBoxContainer>("SettingsContainer");
            _titleLabel = _settingsContainer.GetNode<Label>("TitleLabel");
            _backButton = _settingsContainer.GetNode<Button>("BackButton");

            InitializeVolumeControls();
        }

        /// <summary>
        /// 初始化音量控制组件
        /// </summary>
        private void InitializeVolumeControls()
        {
            var volumeContainer = _settingsContainer.GetNode<VBoxContainer>("VolumeContainer");
            var volumeConfigs = new[]
            {
                ("Master", "MasterVolumeContainer", 100.0),
                ("Music", "MusicVolumeContainer", 80.0),
                ("Sfx", "SfxVolumeContainer", 90.0)
            };

            for (int i = 0; i < volumeConfigs.Length; i++)
            {
                var (name, containerName, defaultValue) = volumeConfigs[i];
                var container = volumeContainer.GetNode<HBoxContainer>(containerName);
                var label = container.GetNode<Label>($"{name}VolumeLabel");
                var slider = container.GetNode<HSlider>($"{name}VolumeSlider");

                _volumeControls[i] = new VolumeControl(name, slider, label, defaultValue);
            }
        }

        /// <summary>
        /// 连接事件
        /// </summary>
        private void ConnectEvents()
        {
            _backButton.Pressed += OnBackPressed;

            for (int i = 0; i < _volumeControls.Length; i++)
            {
                var index = i; // 捕获循环变量
                _volumeControls[i].Slider.ValueChanged += _ => OnVolumeChanged(index);
            }
        }

        /// <summary>
        /// 应用主题到UI元素
        /// </summary>
        private void ApplyThemes()
        {
            ThemeManager.Instance?.CreateBaseThemes();

            // 应用主要组件主题
            ThemeApplicator.ApplyThemeToControl(_titleLabel, "TitleTheme");
            ThemeApplicator.ApplyThemeToControl(_settingsContainer, "DefaultContainerTheme");

            // 为按钮应用主题
            ThemeApplicator.ApplyButtonTheme(_backButton);

            // 应用音量控制主题
            ApplyVolumeControlThemes();
        }

        /// <summary>
        /// 应用音量控制主题
        /// </summary>
        private void ApplyVolumeControlThemes()
        {
            var volumeContainer = _settingsContainer.GetNode<VBoxContainer>("VolumeContainer");
            ThemeApplicator.ApplyThemeToControl(volumeContainer, "DefaultContainerTheme");

            foreach (var control in _volumeControls)
            {
                ThemeApplicator.ApplyThemeToControl(control.Label, "DefaultFontTheme", "DefaultColorTheme");
                ThemeApplicator.ApplyThemeToControl(control.Slider, "DefaultColorTheme", "DefaultStyleTheme");

                // 应用容器主题到音量行容器
                var containerName = $"{control.Name}VolumeContainer";
                var container = volumeContainer.GetNode<HBoxContainer>(containerName);
                ThemeApplicator.ApplyThemeToControl(container, "DefaultContainerTheme");
            }
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            // 设置默认值并更新显示
            for (int i = 0; i < _volumeControls.Length; i++)
            {
                var control = _volumeControls[i];
                control.Slider.Value = control.DefaultValue;
                UpdateVolumeLabel(i);
            }
        }

        /// <summary>
        /// 音量改变事件处理
        /// </summary>
        private void OnVolumeChanged(int index)
        {
            UpdateVolumeLabel(index);
            ApplyVolumeSettings(index);
        }

        /// <summary>
        /// 更新指定音量标签显示
        /// </summary>
        private void UpdateVolumeLabel(int index)
        {
            var control = _volumeControls[index];
            var volumeType = control.Name switch
            {
                "Master" => "主音量",
                "Music" => "音乐音量",
                "Sfx" => "音效音量",
                _ => control.Name
            };
            control.Label.Text = $"{volumeType}: {control.Slider.Value:F0}%";
        }

        /// <summary>
        /// 应用音量设置
        /// </summary>
        /// <param name="index">音量类型索引：0-主音量, 1-音乐音量, 2-音效音量</param>
        private static void ApplyVolumeSettings(int index)
        {
            // TODO: 根据index应用对应的音量设置到音频系统
            _ = index; // 暂时忽略未使用参数警告，等待实际音频系统实现
        }

        /// <summary>返回按钮点击 - 智能返回到来源场景</summary>
        private static void OnBackPressed()
        {
            var previousScene = UIManager.Instance?.GetPreviousScene();
            const string defaultScene = "res://src/Scenes/StartGame/StartGame.tscn";

            // 根据来源场景智能返回，如果是已知场景则返回，否则返回默认场景
            var targetScene = previousScene switch
            {
                "res://src/Scenes/GameViwes/MainWorld.tscn" or
                "res://src/Scenes/StartGame/StartGame.tscn" => previousScene,
                _ => defaultScene
            };

            UIManager.Instance?.SwitchToScene(targetScene);
        }
    }
}
