/*
 * 洞穴入口 - 可交互的洞穴入口对象
 */

using Godot;
using ArchipelagoGame.Interaction;

namespace ArchipelagoGame.Interaction
{
    /// <summary>
    /// 洞穴入口 - 处理洞穴入口的交互逻辑
    /// 继承自Area2D以获得区域检测功能
    /// </summary>
    public partial class CaveEntrance : Area2D
    {
        #region 导出属性
        
        /// <summary>洞穴场景路径</summary>
        [Export]
        public string CaveScenePath { get; set; } = "res://src/Scenes/GameViwes/CaveScene.tscn";
        
        /// <summary>洞穴内玩家生成位置</summary>
        [Export]
        public Vector2 CaveSpawnPosition { get; set; } = new Vector2(400, 300);
        
        /// <summary>洞穴名称</summary>
        [Export]
        public string CaveName { get; set; } = "神秘洞穴";
        
        /// <summary>是否显示交互提示</summary>
        [Export]
        public bool ShowInteractionPrompt { get; set; } = true;
        
        /// <summary>入口图标纹理</summary>
        [Export]
        public Texture2D EntranceTexture { get; set; }
        
        #endregion
        
        #region 私有字段
        
        /// <summary>洞穴入口精灵</summary>
        private Sprite2D _entranceSprite;
        
        /// <summary>交互提示标签</summary>
        private Label _promptLabel;
        
        /// <summary>碰撞形状</summary>
        private CollisionShape2D _collisionShape;
        
        /// <summary>是否鼠标悬停</summary>
        private bool _isMouseOver = false;
        
        /// <summary>原始颜色</summary>
        private Color _originalColor = Colors.White;
        
        #endregion
        
        #region Godot生命周期
        
        public override void _Ready()
        {
            // 设置为可交互组
            AddToGroup("interactable");
            
            // 创建子节点
            CreateChildNodes();
            
            // 连接信号
            InputEvent += OnInputEvent;
            MouseEntered += OnMouseEntered;
            MouseExited += OnMouseExited;
            
            GD.Print($"洞穴入口已初始化: {CaveName}");
        }
        
        #endregion
        
        #region 信号处理
        
        /// <summary>
        /// 处理输入事件（鼠标点击）
        /// </summary>
        private void OnInputEvent(Node viewport, InputEvent @event, long shapeIdx)
        {
            if (@event is InputEventMouseButton mouseEvent)
            {
                if (mouseEvent.Pressed && mouseEvent.ButtonIndex == MouseButton.Left)
                {
                    EnterCave();
                }
            }
        }
        
        /// <summary>
        /// 鼠标进入区域
        /// </summary>
        private void OnMouseEntered()
        {
            _isMouseOver = true;
            
            // 高亮显示入口
            if (_entranceSprite != null)
            {
                _entranceSprite.Modulate = new Color(1.2f, 1.2f, 1.2f);
            }
            
            // 显示交互提示
            if (_promptLabel != null && ShowInteractionPrompt)
            {
                _promptLabel.Visible = true;
            }
            
            GD.Print($"鼠标进入洞穴入口: {CaveName}");
        }
        
        /// <summary>
        /// 鼠标离开区域
        /// </summary>
        private void OnMouseExited()
        {
            _isMouseOver = false;
            
            // 恢复正常显示
            if (_entranceSprite != null)
            {
                _entranceSprite.Modulate = _originalColor;
            }
            
            // 隐藏交互提示
            if (_promptLabel != null)
            {
                _promptLabel.Visible = false;
            }
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 创建子节点
        /// </summary>
        private void CreateChildNodes()
        {
            // 创建碰撞形状
            _collisionShape = new CollisionShape2D();
            _collisionShape.Name = "CollisionShape2D";
            var shape = new RectangleShape2D();
            shape.Size = new Vector2(64, 64); // 默认大小
            _collisionShape.Shape = shape;
            AddChild(_collisionShape);
            
            // 创建精灵节点
            _entranceSprite = new Sprite2D();
            _entranceSprite.Name = "Sprite2D";
            if (EntranceTexture != null)
            {
                _entranceSprite.Texture = EntranceTexture;
            }
            else
            {
                // 如果没有纹理，创建一个简单的占位符
                CreatePlaceholderTexture();
            }
            AddChild(_entranceSprite);
            
            // 创建交互提示标签
            _promptLabel = new Label();
            _promptLabel.Name = "InteractionPrompt";
            _promptLabel.Text = $"点击进入 {CaveName}";
            _promptLabel.Position = new Vector2(-50, -80); // 在入口上方显示
            _promptLabel.Visible = false;
            _promptLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat());
            AddChild(_promptLabel);
        }
        
        /// <summary>
        /// 创建占位符纹理
        /// </summary>
        private void CreatePlaceholderTexture()
        {
            // 创建一个简单的圆形占位符
            var image = Image.Create(64, 64, false, Image.Format.Rgba8);
            image.Fill(new Color(0.5f, 0.3f, 0.1f, 0.8f)); // 棕色半透明
            
            // 绘制一个简单的洞穴入口形状
            for (int x = 0; x < 64; x++)
            {
                for (int y = 0; y < 64; y++)
                {
                    float centerX = 32;
                    float centerY = 32;
                    float distance = Mathf.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    
                    if (distance < 25)
                    {
                        float alpha = 1.0f - (distance / 25.0f);
                        image.SetPixel(x, y, new Color(0.2f, 0.1f, 0.05f, alpha));
                    }
                }
            }
            
            var texture = ImageTexture.CreateFromImage(image);
            _entranceSprite.Texture = texture;
        }
        
        /// <summary>
        /// 进入洞穴
        /// </summary>
        private void EnterCave()
        {
            GD.Print($"玩家进入洞穴: {CaveName} -> {CaveScenePath}");
            
            // 通过交互管理器切换场景
            if (InteractionManager.Instance != null)
            {
                InteractionManager.Instance.TransitionToCave(CaveScenePath, CaveSpawnPosition);
            }
            else
            {
                GD.PrintErr("InteractionManager 实例不存在！");
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置入口纹理
        /// </summary>
        public void SetEntranceTexture(Texture2D texture)
        {
            EntranceTexture = texture;
            if (_entranceSprite != null)
            {
                _entranceSprite.Texture = texture;
            }
        }
        
        /// <summary>
        /// 设置碰撞区域大小
        /// </summary>
        public void SetCollisionSize(Vector2 size)
        {
            if (_collisionShape?.Shape is RectangleShape2D rectShape)
            {
                rectShape.Size = size;
            }
        }
        
        #endregion
    }
}
