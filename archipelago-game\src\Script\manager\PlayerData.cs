/*
 * 玩家数据资源 - 角色属性管理和数据持久化
 */

using Godot;

namespace ArchipelagoGame.Player
{
    /// <summary>
    /// 玩家数据资源 - 负责角色属性管理和数据持久化
    /// 继承自Resource以支持序列化和资源管理
    /// </summary>
    [GlobalClass]
    public partial class PlayerData : Resource
    {
        #region 导出属性 - 可在编辑器中调整

        /// <summary>移动速度（像素/秒）</summary>
        [Export]
        public float Speed { get; set; } = 600.0f;

        /// <summary>移动平滑度 - 值越大移动越平滑</summary>
        [Export]
        public float MovementSmoothing { get; set; } = 10.0f;

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public PlayerData()
        {
            // 使用默认值初始化
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="speed">移动速度</param>
        /// <param name="movementSmoothing">移动平滑度</param>
        public PlayerData(float speed, float movementSmoothing)
        {
            Speed = speed;
            MovementSmoothing = movementSmoothing;
        }

        #endregion
    }
}
