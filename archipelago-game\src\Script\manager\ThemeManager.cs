using Godot;
using System.Collections.Generic;

namespace ArchipelagoGame.Themes
{
    /// <summary>
    /// 主题管理器 - 管理和应用主题
    /// </summary>
    public partial class ThemeManager : Node
    {
        // 单例实例
        private static ThemeManager _instance;
        public static ThemeManager Instance => _instance;

        // 主题字典
        private readonly Dictionary<string, ITheme> _themes = [];

        /// <summary>
        /// Godot生命周期 - 初始化
        /// </summary>
        public override void _Ready()
        {
            _instance = this;
        }

        /// <summary>
        /// 注册主题
        /// </summary>
        /// <param name="theme">要注册的主题</param>
        public void RegisterTheme(ITheme theme)
        {
            _themes.TryAdd(theme.ThemeName, theme);
        }

        /// <summary>
        /// 获取主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        /// <returns>主题实例，如果不存在则返回null</returns>
        public ITheme GetTheme(string themeName) => _themes.GetValueOrDefault(themeName);

        /// <summary>
        /// 应用主题到控件
        /// </summary>
        /// <param name="control">目标控件</param>
        /// <param name="themeName">主题名称</param>
        public void ApplyTheme(Control control, string themeName) => GetTheme(themeName)?.ApplyTo(control);
        
        /// <summary>
        /// 创建并注册基础主题集
        /// </summary>
        public void CreateBaseThemes()
        {
            var fontFile = ResourceLoader.Load<FontFile>("res://Assets/font/汇文仿宋v1.001.ttf");

            // 创建基础主题
            var themes = CreateBasicThemes(fontFile);

            // 注册基础主题
            foreach (var theme in themes.basicThemes)
            {
                RegisterTheme(theme);
            }

            // 创建并注册组合主题
            var compositeThemes = CreateCompositeThemes(themes);
            foreach (var theme in compositeThemes)
            {
                RegisterTheme(theme);
            }
        }

        /// <summary>
        /// 创建基础主题
        /// </summary>
        private static (ITheme[] basicThemes, FontTheme fontTheme, FontTheme titleFontTheme, ColorTheme colorTheme, StyleTheme styleTheme, StyleTheme titleStyleTheme) CreateBasicThemes(FontFile fontFile)
        {
            var fontTheme = new FontTheme(fontFile, 24, "DefaultFontTheme");
            var titleFontTheme = new FontTheme(fontFile, 32, "TitleFontTheme");

            var colorTheme = new ColorTheme(
                new Color(0.9f, 0.9f, 0.9f), // 文本颜色
                new Color(0.2f, 0.2f, 0.3f), // 背景颜色
                "DefaultColorTheme",
                new Color(0.4f, 0.6f, 0.8f)  // 强调色
            );

            var styleTheme = new StyleTheme(
                "DefaultStyleTheme",
                buttonMinHeight: 45,
                buttonMinWidth: 150,
                containerSpacing: 15,
                buttonBackgroundColor: new Color(0.0f, 0.0f, 0.0f, 0.0f), // 完全透明背景
                buttonHoverColor: new Color(0.35f, 0.35f, 0.45f, 0.3f), // 半透明悬停效果
                buttonPressedColor: new Color(0.15f, 0.15f, 0.25f, 0.5f) // 半透明按下效果
            );

            // 创建主菜单专用的30像素间距主题
            var titleStyleTheme = new StyleTheme(
                "TitleStyleTheme",
                buttonMinHeight: 45,
                buttonMinWidth: 150,
                containerSpacing: 30, // 主菜单使用30像素间距
                buttonBackgroundColor: new Color(0.0f, 0.0f, 0.0f, 0.0f), // 完全透明背景
                buttonHoverColor: new Color(0.35f, 0.35f, 0.45f, 0.3f), // 半透明悬停效果
                buttonPressedColor: new Color(0.15f, 0.15f, 0.25f, 0.5f) // 半透明按下效果
            );

            var basicThemes = new ITheme[] { fontTheme, titleFontTheme, colorTheme, styleTheme, titleStyleTheme };
            return (basicThemes, fontTheme, titleFontTheme, colorTheme, styleTheme, titleStyleTheme);
        }

        /// <summary>
        /// 创建组合主题
        /// </summary>
        private static ContainerTheme[] CreateCompositeThemes((ITheme[] basicThemes, FontTheme fontTheme, FontTheme titleFontTheme, ColorTheme colorTheme, StyleTheme styleTheme, StyleTheme titleStyleTheme) themes)
        {
            var containerTheme = new ContainerTheme("DefaultContainerTheme");
            containerTheme.AddTheme(themes.fontTheme);
            containerTheme.AddTheme(themes.colorTheme);
            containerTheme.AddTheme(themes.styleTheme);

            var buttonTheme = new ContainerTheme("DefaultButtonTheme");
            buttonTheme.AddTheme(themes.fontTheme);
            buttonTheme.AddTheme(themes.colorTheme);
            buttonTheme.AddTheme(themes.styleTheme);

            var titleTheme = new ContainerTheme("TitleTheme");
            titleTheme.AddTheme(themes.titleFontTheme);
            titleTheme.AddTheme(themes.colorTheme);

            // 创建主菜单容器主题（使用30像素间距）
            var titleContainerTheme = new ContainerTheme("TitleContainerTheme");
            titleContainerTheme.AddTheme(themes.fontTheme);
            titleContainerTheme.AddTheme(themes.colorTheme);
            titleContainerTheme.AddTheme(themes.titleStyleTheme); // 使用30像素间距的样式主题

            return [containerTheme, buttonTheme, titleTheme, titleContainerTheme];
        }
    }
}