/*
 * UI面板基类 - 所有UI面板的抽象基类
 * 作用：提供UI面板的通用功能实现，继承自Godot的Control节点
 * 继承关系：Control -> BaseUIPanel -> 具体UI面板类
 * 关联：实现IUIPanel接口，自动向UIManager注册
 */

using Godot;
using ArchipelagoGame.Interfaces;

// 玩家接口
namespace ArchipelagoGame.UI
{
    /// <summary>
    /// UI面板抽象基类 - 为所有UI面板提供标准实现
    /// 继承此类需要实现PanelName属性和Initialize方法
    /// </summary>
    public abstract partial class BaseUIPanel : Control, IUIPanel
    {
        /// <summary>面板唯一标识名称 - 子类必须实现</summary>
        public abstract string PanelName { get; }

        /// <summary>面板可见性状态</summary>
        public new bool IsVisible => Visible;

        /// <summary>
        /// Godot生命周期 - 节点准备就绪时调用
        /// 自动执行初始化和注册到UIManager
        /// </summary>
        public override void _Ready()
        {
            Initialize();  // 初始化面板
            UIManager.Instance?.RegisterPanel(this);  // 注册到UI管理器
        }

        /// <summary>显示面板 - 设置为可见并触发显示事件</summary>
        public new virtual void Show()
        {
            Visible = true;
            OnShow();
        }

        /// <summary>隐藏面板 - 设置为不可见并触发隐藏事件</summary>
        public new virtual void Hide()
        {
            Visible = false;
            OnHide();
        }

        /// <summary>初始化面板 - 子类必须实现，用于设置UI元素和事件</summary>
        public abstract void Initialize();

        /// <summary>显示事件钩子 - 子类可重写添加自定义显示逻辑</summary>
        protected virtual void OnShow() { }

        /// <summary>隐藏事件钩子 - 子类可重写添加自定义隐藏逻辑</summary>
        protected virtual void OnHide() { }
    }
}
