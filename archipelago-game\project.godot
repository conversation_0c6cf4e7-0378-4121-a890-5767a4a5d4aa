; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="ArchipelagoGame"
run/main_scene="uid://3ts1illrf1vt"
config/features=PackedStringArray("4.4", "C#")

[autoload]

UiManager="*res://src/Script/manager/UIManager.cs"
ThemeManager="*res://src/Script/manager/ThemeManager.cs"
PopupManager="*res://src/Script/manager/PopupManager.cs"
InteractionManager="*res://src/Script/manager/InteractionManager.cs"

[display]

window/stretch/mode="canvas_items"
window/stretch/scale_mode="integer"

[dotnet]

project/assembly_name="UnnamedProject"

[editor_plugins]

enabled=PackedStringArray("res://addons/gaea/plugin.cfg", "res://addons/gdUnit4/plugin.cfg")

[rendering]

textures/canvas_textures/default_texture_filter=0
driver/stretch/mode="disabled"
